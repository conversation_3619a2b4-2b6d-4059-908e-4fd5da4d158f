import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Directory from '../models/Directory.js';
import Bookmark from '../models/Bookmark.js';
import User from '../models/User.js';

dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/bookmarks-manager';

async function migrateBookmarksToDirectories() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get all users
    const users = await User.find({});
    console.log(`Found ${users.length} users to process`);

    for (const user of users) {
      console.log(`\nProcessing user: ${user.username} (${user._id})`);
      
      // Check if user already has directories
      const existingDirectories = await Directory.find({ owner: user._id });
      
      if (existingDirectories.length > 0) {
        console.log(`  User already has ${existingDirectories.length} directories, skipping...`);
        continue;
      }

      // Create root directory for user
      const rootDirectory = new Directory({
        name: 'Root',
        description: 'Root directory for all bookmarks',
        parent: null,
        owner: user._id,
        visibility: 'private'
      });

      await rootDirectory.save();
      console.log(`  Created root directory: ${rootDirectory._id}`);

      // Create "Imported" directory for existing bookmarks
      const importedDirectory = new Directory({
        name: 'Imported',
        description: 'Bookmarks imported from legacy system',
        parent: rootDirectory._id,
        owner: user._id,
        visibility: 'private'
      });

      await importedDirectory.save();
      console.log(`  Created imported directory: ${importedDirectory._id}`);

      // Move all existing bookmarks to imported directory
      const result = await Bookmark.updateMany(
        { owner: user._id, directory: { $exists: false } },
        { directory: importedDirectory._id }
      );

      console.log(`  Moved ${result.modifiedCount} bookmarks to imported directory`);

      // Handle bookmarks that might have null directory
      const nullResult = await Bookmark.updateMany(
        { owner: user._id, directory: null },
        { directory: importedDirectory._id }
      );

      if (nullResult.modifiedCount > 0) {
        console.log(`  Moved ${nullResult.modifiedCount} additional bookmarks with null directory`);
      }
    }

    console.log('\nMigration completed successfully!');
    
    // Summary
    const totalDirectories = await Directory.countDocuments();
    const totalBookmarks = await Bookmark.countDocuments();
    const bookmarksWithDirectory = await Bookmark.countDocuments({ directory: { $ne: null } });
    
    console.log('\nMigration Summary:');
    console.log(`Total directories created: ${totalDirectories}`);
    console.log(`Total bookmarks: ${totalBookmarks}`);
    console.log(`Bookmarks with directory: ${bookmarksWithDirectory}`);
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run migration
migrateBookmarksToDirectories().catch(console.error);