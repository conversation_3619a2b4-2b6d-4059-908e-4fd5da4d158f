{"manifest_version": 3, "name": "Bookmarks Manager Extension", "version": "1.0", "description": "Add and access bookmarks directly from any webpage.", "permissions": ["contextMenus", "storage", "activeTab", "scripting"], "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}