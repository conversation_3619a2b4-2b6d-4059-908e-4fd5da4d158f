body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  width: 300px;
  background: #f9f9f9;
}
#container {
  padding: 16px;
}
h2 {
  margin-top: 0;
  font-size: 1.2em;
  color: #333;
}
form {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}
input[type="text"], input[type="url"] {
  padding: 6px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
button {
  padding: 8px;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
button:hover {
  background: #1565c0;
}
#bookmarks-list ul {
  list-style: none;
  padding: 0;
}
#bookmarks-list li {
  margin-bottom: 6px;
}
#bookmarks-list a {
  color: #1976d2;
  text-decoration: none;
}
#bookmarks-list a:hover {
  text-decoration: underline;
}
