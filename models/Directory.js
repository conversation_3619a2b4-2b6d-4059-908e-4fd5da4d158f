import mongoose from 'mongoose';

const directorySchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true,
    trim: true,
    maxlength: 100
  },
  description: { 
    type: String, 
    trim: true,
    maxlength: 500
  },
  parent: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Directory',
    default: null
  },
  owner: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User',
    required: true 
  },
  visibility: { 
    type: String, 
    enum: ['private', 'selected', 'public'], 
    default: 'private' 
  },
  sharedWith: [{ 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Update the updatedAt timestamp before saving
directorySchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Prevent circular references
directorySchema.pre('save', async function(next) {
  if (this.parent) {
    // Check if trying to set parent to self
    if (this.parent.toString() === this._id.toString()) {
      return next(new Error('Cannot set directory as its own parent'));
    }
    
    // Check for circular references
    let current = this.parent;
    const visited = new Set([this._id.toString()]);
    
    while (current) {
      if (visited.has(current.toString())) {
        return next(new Error('Circular reference detected'));
      }
      visited.add(current.toString());
      
      const parentDir = await mongoose.model('Directory').findById(current);
      if (!parentDir) break;
      current = parentDir.parent;
    }
  }
  next();
});

// Get all descendants recursively
directorySchema.methods.getDescendants = async function() {
  const descendants = [];
  const stack = [this._id];
  
  while (stack.length > 0) {
    const dirId = stack.pop();
    const children = await mongoose.model('Directory').find({ parent: dirId });
    
    for (const child of children) {
      descendants.push(child);
      stack.push(child._id);
    }
  }
  
  return descendants;
};

// Get full path from root to this directory
directorySchema.methods.getPath = async function() {
  const path = [];
  let current = this;
  
  while (current) {
    path.unshift({
      _id: current._id,
      name: current.name
    });
    current = await mongoose.model('Directory').findById(current.parent);
  }
  
  return path;
};

// Get immediate children
directorySchema.methods.getChildren = function() {
  return mongoose.model('Directory').find({ parent: this._id })
    .sort({ name: 1 });
};

// Get bookmarks in this directory
directorySchema.methods.getBookmarks = function() {
  return mongoose.model('Bookmark').find({ directory: this._id })
    .sort({ createdAt: -1 });
};

// Static method to get root directories for a user
directorySchema.statics.getRootDirectories = function(userId) {
  return this.find({ 
    owner: userId, 
    parent: null 
  }).sort({ name: 1 });
};

// Static method to get directory tree for a user
directorySchema.statics.getDirectoryTree = async function(userId) {
  const directories = await this.find({ owner: userId })
    .sort({ name: 1 });
  
  const tree = {};
  const map = {};
  
  // Create map of all directories
  directories.forEach(dir => {
    map[dir._id.toString()] = { ...dir.toObject(), children: [] };
  });
  
  // Build tree structure
  directories.forEach(dir => {
    if (dir.parent && map[dir.parent.toString()]) {
      map[dir.parent.toString()].children.push(map[dir._id.toString()]);
    } else if (!dir.parent) {
      tree[dir._id.toString()] = map[dir._id.toString()];
    }
  });
  
  return Object.values(tree);
};

const Directory = mongoose.model('Directory', directorySchema);

export default Directory;