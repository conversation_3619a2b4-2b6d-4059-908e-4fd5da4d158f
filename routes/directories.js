import express from 'express';
import Directory from '../models/Directory.js';
import Bookmark from '../models/Bookmark.js';
import { auth } from '../middleware/auth.js';

const router = express.Router();

// @route   GET /api/directories
// @desc    Get all directories for authenticated user
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const directories = await Directory.find({ owner: req.user.id })
      .populate('parent', 'name')
      .sort({ name: 1 });
    
    res.json(directories);
  } catch (err) {
    console.error('Error fetching directories:', err);
    res.status(500).json({ message: err.message });
  }
});

// @route   GET /api/directories/tree
// @desc    Get directory tree structure for authenticated user
// @access  Private
router.get('/tree', auth, async (req, res) => {
  try {
    const tree = await Directory.getDirectoryTree(req.user.id);
    res.json(tree);
  } catch (err) {
    console.error('Error fetching directory tree:', err);
    res.status(500).json({ message: err.message });
  }
});

// @route   GET /api/directories/:id
// @desc    Get a single directory with its contents
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const directory = await Directory.findOne({ 
      _id: req.params.id, 
      owner: req.user.id 
    }).populate('parent', 'name');
    
    if (!directory) {
      return res.status(404).json({ message: 'Directory not found' });
    }
    
    // Get subdirectories
    const subdirectories = await Directory.find({ 
      parent: directory._id 
    }).sort({ name: 1 });
    
    // Get bookmarks in this directory
    const bookmarks = await Bookmark.find({ 
      directory: directory._id,
      owner: req.user.id 
    }).sort({ createdAt: -1 });
    
    res.json({
      directory,
      subdirectories,
      bookmarks
    });
  } catch (err) {
    console.error('Error fetching directory:', err);
    res.status(500).json({ message: err.message });
  }
});

// @route   POST /api/directories
// @desc    Create a new directory
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    const { name, description, parent } = req.body;
    
    // Validate parent directory exists and belongs to user
    if (parent) {
      const parentDir = await Directory.findOne({ 
        _id: parent, 
        owner: req.user.id 
      });
      
      if (!parentDir) {
        return res.status(404).json({ message: 'Parent directory not found' });
      }
    }
    
    const directory = new Directory({
      name,
      description,
      parent: parent || null,
      owner: req.user.id
    });
    
    await directory.save();
    
    res.status(201).json(directory);
  } catch (err) {
    console.error('Error creating directory:', err);
    res.status(400).json({ message: err.message });
  }
});

// @route   PUT /api/directories/:id
// @desc    Update a directory
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { name, description, parent } = req.body;
    
    const directory = await Directory.findOne({ 
      _id: req.params.id, 
      owner: req.user.id 
    });
    
    if (!directory) {
      return res.status(404).json({ message: 'Directory not found' });
    }
    
    // Validate new parent if provided
    if (parent && parent !== directory.parent?.toString()) {
      const newParent = await Directory.findOne({ 
        _id: parent, 
        owner: req.user.id 
      });
      
      if (!newParent) {
        return res.status(404).json({ message: 'New parent directory not found' });
      }
      
      // Prevent moving directory into itself or its descendants
      const descendants = await directory.getDescendants();
      if (descendants.some(d => d._id.toString() === parent)) {
        return res.status(400).json({ message: 'Cannot move directory into itself or its descendants' });
      }
    }
    
    directory.name = name || directory.name;
    directory.description = description !== undefined ? description : directory.description;
    directory.parent = parent !== undefined ? parent : directory.parent;
    directory.updatedAt = new Date();
    
    await directory.save();
    
    res.json(directory);
  } catch (err) {
    console.error('Error updating directory:', err);
    res.status(400).json({ message: err.message });
  }
});

// @route   DELETE /api/directories/:id
// @desc    Delete a directory and optionally its contents
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const { force = false } = req.query;
    
    const directory = await Directory.findOne({ 
      _id: req.params.id, 
      owner: req.user.id 
    });
    
    if (!directory) {
      return res.status(404).json({ message: 'Directory not found' });
    }
    
    // Check if directory has subdirectories
    const subdirectories = await Directory.find({ parent: directory._id });
    if (subdirectories.length > 0 && !force) {
      return res.status(400).json({ 
        message: 'Directory contains subdirectories. Use force=true to delete recursively.' 
      });
    }
    
    // Check if directory has bookmarks
    const bookmarks = await Bookmark.find({ directory: directory._id });
    if (bookmarks.length > 0 && !force) {
      return res.status(400).json({ 
        message: 'Directory contains bookmarks. Use force=true to delete recursively.' 
      });
    }
    
    // Delete recursively if force=true
    if (force) {
      // Delete all subdirectories recursively
      const descendants = await directory.getDescendants();
      for (const subDir of descendants) {
        await Directory.findByIdAndDelete(subDir._id);
      }
      
      // Move bookmarks to parent directory or root
      const newDirectory = directory.parent || null;
      await Bookmark.updateMany(
        { directory: directory._id },
        { directory: newDirectory }
      );
    } else {
      // Move bookmarks to parent directory or root
      const newDirectory = directory.parent || null;
      await Bookmark.updateMany(
        { directory: directory._id },
        { directory: newDirectory }
      );
    }
    
    // Delete the directory
    await Directory.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'Directory deleted successfully' });
  } catch (err) {
    console.error('Error deleting directory:', err);
    res.status(500).json({ message: err.message });
  }
});

// @route   POST /api/directories/:id/move-bookmarks
// @desc    Move bookmarks to another directory
// @access  Private
router.post('/:id/move-bookmarks', auth, async (req, res) => {
  try {
    const { bookmarkIds, targetDirectoryId } = req.body;
    
    if (!Array.isArray(bookmarkIds) || bookmarkIds.length === 0) {
      return res.status(400).json({ message: 'bookmarkIds array is required' });
    }
    
    // Validate source directory
    const sourceDirectory = await Directory.findOne({ 
      _id: req.params.id, 
      owner: req.user.id 
    });
    
    if (!sourceDirectory) {
      return res.status(404).json({ message: 'Source directory not found' });
    }
    
    // Validate target directory
    let targetDirectory = null;
    if (targetDirectoryId) {
      targetDirectory = await Directory.findOne({ 
        _id: targetDirectoryId, 
        owner: req.user.id 
      });
      
      if (!targetDirectory) {
        return res.status(404).json({ message: 'Target directory not found' });
      }
    }
    
    // Validate bookmarks
    const bookmarks = await Bookmark.find({ 
      _id: { $in: bookmarkIds },
      owner: req.user.id 
    });
    
    if (bookmarks.length !== bookmarkIds.length) {
      return res.status(404).json({ message: 'Some bookmarks not found or not owned by user' });
    }
    
    // Move bookmarks
    await Bookmark.updateMany(
      { _id: { $in: bookmarkIds } },
      { directory: targetDirectoryId || null }
    );
    
    res.json({ message: 'Bookmarks moved successfully' });
  } catch (err) {
    console.error('Error moving bookmarks:', err);
    res.status(500).json({ message: err.message });
  }
});

export default router;