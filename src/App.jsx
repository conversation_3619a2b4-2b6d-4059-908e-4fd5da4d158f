import React, { useState, useEffect } from 'react';
import BookmarkGrid from './components/BookmarkGrid';
import BookmarkDetail from './components/BookmarkDetail';
import SearchBar from './components/SearchBar';
import AddBookmarkForm from './components/AddBookmarkForm';
import FontSettingsModal from './components/FontSettingsModal';
import TagManager from './components/TagManager';
import DirectoryTree from './components/DirectoryTree';
import DirectoryBreadcrumb from './components/DirectoryBreadcrumb';
import DirectoryManager from './components/DirectoryManager';
import FolderActions from './components/FolderActions';
import { loadFontSettings, saveFontSettings } from './utils/fontSettings';
import { Settings, Grid, List, Copy, Upload, Bookmark as BookmarkIcon, Tags, Plus, Folder } from 'lucide-react';
import AuthModal from './components/Auth/AuthModal';
import { LogIn, LogOut, User } from 'lucide-react';
import api from './utils/api';

const App = () => {
  const [bookmarks, setBookmarks] = useState([]);
  const [filteredBookmarks, setFilteredBookmarks] = useState([]);
  const [selectedBookmark, setSelectedBookmark] = useState(null);
  const [viewMode, setViewMode] = useState('grid');
  const [initialFormData, setInitialFormData] = useState(null);
  const [fontSettings, setFontSettings] = useState({
    titleFontFamily: 'Arial',
    titleFontSize: 16,
    titleFontWeight: 'bold',
    titleFontColor: '#000000',
    descriptionFontFamily: 'Arial',
    descriptionFontSize: 14,
    descriptionFontWeight: 'normal',
    descriptionFontColor: '#333333',
  });
  const [isFontSettingsModalOpen, setIsFontSettingsModalOpen] = useState(false);
  const [isTagManagerOpen, setIsTagManagerOpen] = useState(false);
  const [hoverText, setHoverText] = useState('');
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [currentDirectory, setCurrentDirectory] = useState(null);
  const [directories, setDirectories] = useState([]);
  const [showDirectoryManager, setShowDirectoryManager] = useState(false);
  const [directoryMode, setDirectoryMode] = useState('create');
  const [selectedDirectory, setSelectedDirectory] = useState(null);

  // Load font settings on mount
  useEffect(() => {
    const savedSettings = loadFontSettings();
    if (savedSettings) {
      setFontSettings(savedSettings);
    }
  }, []);

  // Save font settings when they change
  useEffect(() => {
    saveFontSettings(fontSettings);
  }, [fontSettings]);

  // Fetch bookmarks based on current directory
  useEffect(() => {
    fetchBookmarks();
  }, [currentDirectory, currentUser]);

  // Fetch directories
  useEffect(() => {
    fetchDirectories();
  }, [currentUser]);

  const fetchBookmarks = async () => {
    try {
      let data;
      if (currentDirectory) {
        const response = await api.get(`/directories/${currentDirectory}`);
        data = response.data.bookmarks;
      } else {
        const response = await api.get('/bookmarks');
        data = response.data;
      }
      setBookmarks(data);
      setFilteredBookmarks(data);
    } catch (error) {
      console.error('Error fetching bookmarks:', error);
    }
  };

  const fetchDirectories = async () => {
    try {
      const response = await api.get('/directories');
      setDirectories(response.data);
    } catch (error) {
      console.error('Error fetching directories:', error);
    }
  };

  // Handle bookmarklet data
  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    const url = queryParams.get('url');
    const title = queryParams.get('title');
    const description = queryParams.get('description');
    const favicon = queryParams.get('favicon');

    if (url) {
      setInitialFormData({
        url,
        title: title || 'Untitled',
        description: description || '',
        favicon: favicon || `https://www.google.com/s2/favicons?domain=${new URL(url).hostname}`,
      });

      // Clear the query parameters after processing
      window.history.replaceState({}, document.title, window.location.pathname);
      setIsFormVisible(true); // Open form when bookmarklet is used
    }
  }, []);

  // Add a new bookmark
  const handleAddBookmark = async (bookmark) => {
    console.log('Adding new bookmark:', bookmark);
      try {
          // Generate favicon if not provided
          const favicon = bookmark.favicon ||
              `https://www.google.com/s2/favicons?domain=${new URL(bookmark.url).hostname}`;
          
          const { data } = await api.post('/bookmarks', {
              ...bookmark,
              favicon
          });
          
          setBookmarks([...bookmarks, data]);
          setFilteredBookmarks([...bookmarks, data]);
          setInitialFormData(null); // Clear initial form data after adding
      } catch (error) {
          console.error('Error adding bookmark:', error);
      }
  };

  // Edit a bookmark
  const handleEditBookmark = async (updatedBookmark) => {
    try {
      const { data } = await api.put(`/bookmarks/${updatedBookmark._id}`, updatedBookmark);
      
      const updatedBookmarks = bookmarks.map((bookmark) =>
        bookmark._id === data._id ? data : bookmark
      );
      setBookmarks(updatedBookmarks);
      setFilteredBookmarks(updatedBookmarks);
    } catch (error) {
      console.error('Error updating bookmark:', error);
      alert(`Failed to update bookmark: ${error.response?.data?.message || error.message}`);
    }
  };

  // Delete a bookmark
  const handleDeleteBookmark = async (id) => {
    try {
      await api.delete(`/bookmarks/${id}`);
      const updatedBookmarks = bookmarks.filter((bookmark) => bookmark._id !== id);
      setBookmarks(updatedBookmarks);
      setFilteredBookmarks(updatedBookmarks);
    } catch (error) {
      console.error('Error deleting bookmark:', error);
    }
  };

  // Search bookmarks
  const handleSearch = (query) => {
    const filtered = bookmarks.filter(
      (bookmark) =>
        bookmark.title.toLowerCase().includes(query.toLowerCase()) ||
        bookmark.description.toLowerCase().includes(query.toLowerCase()) ||
        bookmark.tags.some((tag) => tag.toLowerCase().includes(query.toLowerCase()))
    );
    setFilteredBookmarks(filtered);
  };

  // Toggle view mode
  const toggleViewMode = () => {
    setViewMode((prevMode) => (prevMode === 'grid' ? 'list' : 'grid'));
  };

  // Apply font settings
  const handleApplyFontSettings = (settings) => {
    setFontSettings(settings);
  };

  // Copy filtered bookmarks to clipboard as JSON
  const handleCopyBookmarks = () => {
    const jsonString = JSON.stringify(filteredBookmarks, null, 2);
    navigator.clipboard.writeText(jsonString)
      .then(() => {
        alert('Filtered bookmarks copied to clipboard!');
      })
      .catch((err) => {
        console.error('Failed to copy bookmarks:', err);
        alert('Failed to copy bookmarks to clipboard.');
      });
  };

  // Import bookmarks from a JSON file - only allowed for logged-in users
  const handleImportBookmarks = (event) => {
    // Check if user is logged in
    if (!currentUser) {
      alert('You must be logged in to import bookmarks.');
      return;
    }

    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const importedBookmarks = JSON.parse(e.target.result);

        // Validate the imported data
        if (!Array.isArray(importedBookmarks)) {
          throw new Error('Invalid file format: Expected an array of bookmarks.');
        }

        // Process each bookmark
        const processedBookmarks = importedBookmarks.map((bookmark) => ({
          ...bookmark,
          favicon: `https://www.google.com/s2/favicons?domain=${new URL(bookmark.url).hostname}`,
        }));

        // Add the processed bookmarks to the database
        const { data } = await api.post('/bookmarks', processedBookmarks);
        setBookmarks([...bookmarks, ...data]);
        setFilteredBookmarks([...bookmarks, ...data]);

        alert('Bookmarks imported successfully!');
      } catch (error) {
        console.error('Error importing bookmarks:', error);
        alert(`Failed to import bookmarks: ${error.response?.data?.message || error.message}`);
      }
    };
    reader.readAsText(file);
  };

  const bookmarkletCode = `javascript:(function() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    const description = encodeURIComponent(window.getSelection().toString().trim() || '');
    const favicon = encodeURIComponent(document.querySelector('link[rel*="icon"]')?.href || \`https://www.google.com/s2/favicons?domain=\${window.location.hostname}\`);
    const appUrl = \`http://localhost:5173/?url=\${url}&title=\${title}&description=\${description}&favicon=\${favicon}\`;
    window.open(appUrl, '_blank');
  })();`;

  const handleLogin = (user) => {
    setCurrentUser(user);
  };
  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setCurrentUser(null);
  };
  
  const authButton = currentUser ? (
    <div className="flex items-center space-x-3">
      <div className="flex items-center space-x-2">
        <User size={20} className="text-gray-600" />
        <span className="text-sm font-medium text-gray-700">
          {currentUser.username}
        </span>
      </div>
      <button
        onClick={handleLogout}
        className="flex items-center text-sm bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded transition-colors duration-200"
      >
        <LogOut size={16} className="mr-1" />
        <span>Logout</span>
      </button>
    </div>
  ) : (
    <button
      onClick={() => setIsAuthModalOpen(true)}
      className="flex items-center text-sm bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded transition-colors duration-200"
    >
      <LogIn size={16} className="mr-1" />
      <span>Login / Register</span>
    </button>
  );

  const handleDirectoryAction = (mode, directory = null) => {
    setDirectoryMode(mode);
    setSelectedDirectory(directory);
    setShowDirectoryManager(true);
  };

  const handleDirectorySuccess = (result) => {
    setShowDirectoryManager(false);
    fetchDirectories();
    if (directoryMode === 'create' && currentDirectory === null) {
      setCurrentDirectory(result._id);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold">Bookmarks Manager</h1>
        {authButton}
      </div>

      <div className="flex gap-4">
        {/* Sidebar with Directory Tree */}
        <div className="w-64 bg-gray-50 rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Folders</h2>
            <FolderActions
              directory={currentDirectory}
              onFolderCreated={handleDirectorySuccess}
              onFolderUpdated={handleDirectorySuccess}
              onFolderDeleted={handleDirectorySuccess}
            />
          </div>
          
          <DirectoryTree
            selectedDirectory={currentDirectory}
            onDirectorySelect={setCurrentDirectory}
            onDirectoryCreate={() => handleDirectoryAction('create')}
            onDirectoryEdit={(dir) => handleDirectoryAction('edit', dir)}
            onDirectoryDelete={(dir) => handleDirectoryAction('delete', dir)}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-4">
            <button
              onClick={() => setIsFormVisible(!isFormVisible)}
              className="p-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
            >
              <Plus size={24} />
            </button>
            <SearchBar onSearch={handleSearch} />
          </div>

          <DirectoryBreadcrumb
            currentDirectory={currentDirectory}
            onDirectorySelect={setCurrentDirectory}
          />

          {isFormVisible && (
            <AddBookmarkForm
              onAdd={handleAddBookmark}
              initialData={initialFormData}
              onCancel={() => setIsFormVisible(false)}
            />
          )}

          <BookmarkGrid
            bookmarks={filteredBookmarks}
            onDelete={handleDeleteBookmark}
            onEdit={handleEditBookmark}
            viewMode={viewMode}
            fontSettings={fontSettings}
            onSelect={setSelectedBookmark}
            currentDirectory={currentDirectory}
          />
        </div>
      </div>

      <DirectoryManager
        isOpen={showDirectoryManager}
        onClose={() => setShowDirectoryManager(false)}
        directory={selectedDirectory}
        parentDirectory={directoryMode === 'create' ? currentDirectory : null}
        onSuccess={handleDirectorySuccess}
        mode={directoryMode}
      />
    </div>
  );
};

export default App;
