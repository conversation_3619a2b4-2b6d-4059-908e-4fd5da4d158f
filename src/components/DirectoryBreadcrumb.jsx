import React, { useState, useEffect } from 'react';
import { ChevronRight, Home } from 'lucide-react';
import api from '../utils/api';

const DirectoryBreadcrumb = ({ currentDirectory, onDirectorySelect }) => {
  const [path, setPath] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (currentDirectory) {
      fetchPath(currentDirectory);
    } else {
      setPath([]);
    }
  }, [currentDirectory]);

  const fetchPath = async (directoryId) => {
    try {
      setLoading(true);
      const { data } = await api.get(`/directories/${directoryId}`);
      setPath(data.directory.path || []);
    } catch (error) {
      console.error('Error fetching directory path:', error);
      setPath([]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center space-x-2 px-4 py-2">
        <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
        <ChevronRight size={16} className="text-gray-400" />
        <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
    </div>
    );
  }

  return (
    <nav className="flex items-center space-x-1 px-4 py-2 bg-gray-50 border-b">
      <button
        onClick={() => onDirectorySelect(null)}
        className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 transition-colors"
        title="Go to root"
      >
        <Home size={16} />
        <span>All Bookmarks</span>
      </button>

      {path.map((segment, index) => (
        <React.Fragment key={segment._id}>
          <ChevronRight size={16} className="text-gray-400" />
          <button
            onClick={() => onDirectorySelect(segment._id)}
            className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
          >
            {segment.name}
          </button>
        </React.Fragment>
      ))}
    </nav>
  );
};

export default DirectoryBreadcrumb;