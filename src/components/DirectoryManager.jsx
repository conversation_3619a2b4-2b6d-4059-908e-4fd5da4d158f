import React, { useState, useEffect } from 'react';
import { X, FolderPlus, Folder, Edit3, Trash2, Alert<PERSON>riangle } from 'lucide-react';
import api from '../utils/api';

const DirectoryManager = ({ 
  isOpen, 
  onClose, 
  directory, 
  parentDirectory, 
  onSuccess,
  mode = 'create'
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [directories, setDirectories] = useState([]);

  useEffect(() => {
    if (isOpen) {
      fetchDirectories();
      if (mode === 'edit' && directory) {
        setFormData({
          name: directory.name || '',
          description: directory.description || ''
        });
      } else if (mode === 'create') {
        setFormData({ name: '', description: '' });
      }
    }
  }, [isOpen, directory, mode]);

  const fetchDirectories = async () => {
    try {
      const { data } = await api.get('/directories');
      setDirectories(data);
    } catch (error) {
      console.error('Error fetching directories:', error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      let response;
      const payload = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        parent: parentDirectory || null
      };

      if (mode === 'create') {
        response = await api.post('/directories', payload);
      } else if (mode === 'edit' && directory) {
        response = await api.put(`/directories/${directory._id}`, payload);
      }

      onSuccess(response.data);
      onClose();
    } catch (error) {
      console.error('Error saving directory:', error);
      setError(error.response?.data?.message || 'Failed to save directory');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    setLoading(true);
    setError(null);

    try {
      await api.delete(`/directories/${directory._id}?force=true`);
      onSuccess(directory);
      onClose();
    } catch (error) {
      console.error('Error deleting directory:', error);
      setError(error.response?.data?.message || 'Failed to delete directory');
    } finally {
      setLoading(false);
    }
  };

  const renderCreateForm = () => (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Folder Name *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter folder name"
          required
          maxLength={100}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter folder description (optional)"
          rows={3}
          maxLength={500}
        />
      </div>

      {parentDirectory && (
        <div className="text-sm text-gray-600">
          <p>Parent folder: {directories.find(d => d._id === parentDirectory)?.name || 'Root'}</p>
        </div>
      )}
    </form>
  );

  const renderEditForm = () => (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Folder Name *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
          maxLength={100}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={3}
          maxLength={500}
        />
      </div>
    </form>
  );

  const renderDeleteConfirmation = () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-3 text-red-600">
        <AlertTriangle size={24} />
        <h3 className="text-lg font-medium">Delete Folder</h3>
      </div>
      
      <p className="text-gray-700">
        Are you sure you want to delete the folder "<strong>{directory?.name}</strong>"?
      </p>
      
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
        <p className="text-sm text-yellow-800">
          This action will move all bookmarks in this folder to the parent folder or root.
          Subfolders will also be moved.
        </p>
      </div>
    </div>
  );

  const getTitle = () => {
    switch (mode) {
      case 'create': return 'Create New Folder';
      case 'edit': return 'Edit Folder';
      case 'delete': return 'Delete Folder';
      default: return 'Folder Manager';
    }
  };

  const getIcon = () => {
    switch (mode) {
      case 'create': return <FolderPlus size={20} />;
      case 'edit': return <Edit3 size={20} />;
      case 'delete': return <Trash2 size={20} />;
      default: return <Folder size={20} />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2">
            {getIcon()}
            <h2 className="text-lg font-semibold">{getTitle()}</h2>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-4">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {mode === 'create' && renderCreateForm()}
          {mode === 'edit' && renderEditForm()}
          {mode === 'delete' && renderDeleteConfirmation()}
        </div>

        <div className="flex justify-end space-x-3 p-4 border-t">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
            disabled={loading}
          >
            Cancel
          </button>
          
          {mode === 'delete' ? (
            <button
              onClick={handleDelete}
              disabled={loading}
              className="px-4 py-2 text-sm text-white bg-red-600 hover:bg-red-700 rounded-md disabled:opacity-50"
            >
              {loading ? 'Deleting...' : 'Delete'}
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={loading || !formData.name.trim()}
              className="px-4 py-2 text-sm text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50"
            >
              {loading ? 'Saving...' : 'Save'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DirectoryManager;