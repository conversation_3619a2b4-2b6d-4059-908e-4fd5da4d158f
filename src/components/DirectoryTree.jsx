import React, { useState, useEffect } from 'react';
import { ChevronRight, ChevronDown, Folder, FolderOpen, Plus } from 'lucide-react';
import api from '../utils/api';

const DirectoryTree = ({ 
  selectedDirectory, 
  onDirectorySelect, 
  onDirectoryCreate,
  onDirectoryEdit,
  onDirectoryDelete 
}) => {
  const [directories, setDirectories] = useState([]);
  const [expandedDirs, setExpandedDirs] = useState(new Set());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDirectories();
  }, []);

  const fetchDirectories = async () => {
    try {
      setLoading(true);
      const { data } = await api.get('/directories');
      setDirectories(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching directories:', err);
      setError('Failed to load directories');
    } finally {
      setLoading(false);
    }
  };

  const toggleDirectory = (dirId) => {
    const newExpanded = new Set(expandedDirs);
    if (newExpanded.has(dirId)) {
      newExpanded.delete(dirId);
    } else {
      newExpanded.add(dirId);
    }
    setExpandedDirs(newExpanded);
  };

  const handleDirectoryClick = (directory, event) => {
    if (event.button === 2) { // Right click
      event.preventDefault();
    } else {
      onDirectorySelect(directory._id);
    }
  };

  const buildTree = (directories) => {
    const tree = {};
    const map = {};
    
    directories.forEach(dir => {
      map[dir._id] = { ...dir, children: [] };
    });
    
    directories.forEach(dir => {
      if (dir.parent) {
        if (map[dir.parent]) {
          map[dir.parent].children.push(map[dir._id]);
        }
      } else {
        tree[dir._id] = map[dir._id];
      }
    });
    
    return Object.values(tree);
  };

  const renderDirectory = (directory, level = 0) => {
    const isSelected = selectedDirectory === directory._id;
    const hasChildren = directory.children && directory.children.length > 0;

    return (
      <div key={directory._id} className="select-none">
        <div
          className={`flex items-center py-1 px-2 cursor-pointer hover:bg-gray-100 rounded ${
            isSelected ? 'bg-blue-100' : ''
          }`}
          style={{ paddingLeft: `${level * 16 + 8}px` }}
          onClick={(e) => handleDirectoryClick(directory, e)}
        >
          <div className="flex items-center flex-1">
            {hasChildren ? (
              <button
                className="mr-1 p-0.5 hover:bg-gray-200 rounded"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleDirectory(directory._id);
                }}
              >
                {expandedDirs.has(directory._id) ? (
                  <ChevronDown size={16} />
                ) : (
                  <ChevronRight size={16} />
                )}
              </button>
            ) : (
              <div className="w-6 mr-1" />
            )}
            
            {expandedDirs.has(directory._id) ? (
              <FolderOpen size={16} className="mr-2 text-blue-600" />
            ) : (
              <Folder size={16} className="mr-2 text-blue-600" />
            )}
            
            <span className="text-sm truncate">{directory.name}</span>
            
            {directory.children && directory.children.length > 0 && (
              <span className="ml-1 text-xs text-gray-500">
                ({directory.children.length})
              </span>
            )}
          </div>
        </div>
        
        {expandedDirs.has(directory._id) && hasChildren && (
          <div>
            {directory.children.map(child => renderDirectory(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 ml-4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 ml-4"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center text-red-600">
        <p>{error}</p>
        <button
          onClick={fetchDirectories}
          className="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-sm"
        >
          Retry
        </button>
      </div>
    );
  }

  const tree = buildTree(directories);

  return (
    <div className="relative">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-sm font-semibold">Folders</h3>
      </div>

      <div className="space-y-1">
        <div
          className={`flex items-center py-1 px-2 cursor-pointer hover:bg-gray-100 rounded ${
            selectedDirectory === null ? 'bg-blue-100' : ''
          }`}
          onClick={() => onDirectorySelect(null)}
        >
          <Folder size={16} className="mr-2 text-gray-600" />
          <span className="text-sm">All Bookmarks</span>
        </div>

        {tree.map(directory => renderDirectory(directory))}
      </div>
    </div>
  );
};

export default DirectoryTree;