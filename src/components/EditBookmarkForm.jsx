import React, { useState, useEffect } from 'react';
import { fetchMetadata } from '../utils/fetchMetadata';
import FolderSelector from './FolderSelector.jsx';
import { useFolders } from '../contexts/FolderContext.jsx';

const EditBookmarkForm = ({ bookmark, onUpdate, onCancel }) => {
  const { folders } = useFolders();
  const [url, setUrl] = useState(bookmark.url || '');
  const [title, setTitle] = useState(bookmark.title || '');
  const [description, setDescription] = useState(bookmark.description || '');
  const [tags, setTags] = useState(bookmark.tags?.join(', ') || '');
  const [visibility, setVisibility] = useState(bookmark.visibility || 'private');
  const [folder, setFolder] = useState(bookmark.folder || null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    
    try {
      const updatedBookmark = {
        ...bookmark,
        url: url.trim(),
        title: title.trim(),
        description: description.trim(),
        tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        visibility,
        folder
      };
      
      await onUpdate(updatedBookmark);
    } catch (err) {
      setError('Failed to update bookmark');
      console.error('Error updating bookmark:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-6 bg-white rounded-lg shadow">
      {error && (
        <div className="text-red-500 text-sm mb-2">{error}</div>
      )}
      
      <div>
        <label className="block text-base font-medium text-gray-700 pb-1">
          URL
          <input
            type="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            className="mt-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 p-2"
            required
          />
        </label>
      </div>
      
      <div>
        <label className="block text-base font-medium text-gray-700 pb-1">
          Title
          <input
            type="text"
            value极={title}
            onChange={(e) => setTitle(e.target.value)}
            className="mt-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 p-2"
            required
          />
        </label>
      </div>
      
      <div>
        <label className="block text-base font-medium text-gray-700 pb-1">
          Description
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="mt-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 p-2"
            rows="3"
          />
        </label>
      </div>
      
      <div>
        <label className="block text-base font-medium text-gray-700 pb-1">
          Tags (comma-separated)
          <input
            type="text"
            value={tags}
            onChange={(e) => setTags(e.target.value)}
            className="mt-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 p极-2"
            placeholder="tag1, tag2, tag3"
          />
        </label>
      </div>
      
      <div>
        <label className="block text-base font-medium text-gray-700 pb-1">
          Folder
          <FolderSelector 
            selectedFolder={folder}
            onSelect={setFolder}
          />
        </label>
      </div>
      
      <div>
        <label className="block text-base font-medium text-gray-700 pb-1">
          Visibility
          <select
            value={visibility}
            onChange={(e) => setVisibility(e.target.value)}
            className="mt-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 p-2"
          >
            <option value="private">Private</option>
            <option value="public">Public</option>
          </select>
        </label>
      </div>
      
      <div className="flex justify-end space-x-2">
        <button
          type="button"
          onClick={onCancel}
          className="px-5 py-2 text-base font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="px-5 py-2 text-base font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {isLoading ? 'Updating...' : 'Update Bookmark'}
        </button>
      </div>
    </form>
  );
};

export default EditBookmarkForm;
