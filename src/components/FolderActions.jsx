import React, { useState } from 'react';
import { FolderPlus, Edit3, Trash2 } from 'lucide-react';
import DirectoryManager from './DirectoryManager';

const FolderActions = ({ 
  directory, 
  onFolderCreated, 
  onFolderUpdated, 
  onFolderDeleted 
}) => {
  const [showManager, setShowManager] = useState(false);
  const [mode, setMode] = useState('create');

  const handleAction = (action) => {
    setMode(action);
    setShowManager(true);
  };

  const handleSuccess = (result) => {
    setShowManager(false);
    if (mode === 'create' && onFolderCreated) {
      onFolderCreated(result);
    } else if (mode === 'edit' && onFolderUpdated) {
      onFolderUpdated(result);
    } else if (mode === 'delete' && onFolderDeleted) {
      onFolderDeleted(result);
    }
  };

  return (
    <div className="flex space-x-2">
      {directory ? (
        <>
          <button
            onClick={() => handleAction('edit')}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded"
            title="Edit folder"
          >
            <Edit3 size={16} />
          </button>
          <button
            onClick={() => handleAction('delete')}
            className="p-2 text-red-600 hover:bg-red-50 rounded"
            title="Delete folder"
          >
            <Trash2 size={16} />
          </button>
        </>
      ) : null}
      
      <button
        onClick={() => handleAction('create')}
        className="flex items-center space-x-1 px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
      >
        <FolderPlus size={16} />
        <span>New Folder</span>
      </button>

      <DirectoryManager
        isOpen={showManager}
        onClose={() => setShowManager(false)}
        directory={mode !== 'create' ? directory : null}
        parentDirectory={mode === 'create' ? directory?._id : null}
        onSuccess={handleSuccess}
        mode={mode}
      />
    </div>
  );
};

export default FolderActions;