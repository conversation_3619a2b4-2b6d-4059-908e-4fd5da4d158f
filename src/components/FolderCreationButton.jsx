import React, { useState } from 'react';
import { FolderPlus } from 'lucide-react';
import DirectoryManager from './DirectoryManager';

const FolderCreationButton = ({ parentDirectory = null, onFolderCreated }) => {
  const [showManager, setShowManager] = useState(false);

  const handleFolderCreated = (folder) => {
    setShowManager(false);
    if (onFolderCreated) {
      onFolderCreated(folder);
    }
  };

  return (
    <>
      <button
        onClick={() => setShowManager(true)}
        className="flex items-center space-x-2 px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
      >
        <FolderPlus size={16} />
        <span>Create Folder</span>
      </button>

      <DirectoryManager
        isOpen={showManager}
        onClose={() => setShowManager(false)}
        parentDirectory={parentDirectory}
        onSuccess={handleFolderCreated}
        mode="create"
      />
    </>
  );
};

export default FolderCreationButton;