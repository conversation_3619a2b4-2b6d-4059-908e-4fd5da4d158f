import React, { useState } from 'react';
import { useFolders } from '../contexts/FolderContext.jsx';
import { ChevronDown, Folder, FolderOpen } from 'lucide-react';

const FolderSelector = ({ selectedFolder, onSelect }) => {
  const { folders } = useFolders();
  const [isOpen, setIsOpen] = useState(false);
  
  const toggleDropdown = () => setIsOpen(!isOpen);
  
  const handleSelect = (folderId) => {
    onSelect(folderId);
    setIsOpen(false);
  };
  
  // Find the selected folder object
  const selected = folders.find(f => f._id === selectedFolder);
  
  return (
    <div className="relative w-full">
      <div 
        className="flex items-center justify-between border border-gray-300 rounded p-2 cursor-pointer hover:border-gray-400"
        onClick={toggleDropdown}
      >
        <div className="flex items-center truncate">
          {selected ? (
            <>
              <Folder size={16} className="mr-2 flex-shrink-0" />
              <span className="truncate">{selected.name}</span>
            </>
          ) : (
            <span className="text-gray-500">Select a folder...</span>
          )}
        </div>
        <ChevronDown size={16} className={`transform transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </div>
      
      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded shadow-lg max-h-60 overflow-y-auto">
          {folders.map(folder => (
            <div 
              key={folder._id} 
              className="flex items-center p-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => handleSelect(folder._id)}
            >
              <div className="w-4 mr-2 flex-shrink-0">
                {folders.some(f => f.parent === folder._id) ? <FolderOpen size={16} /> : <Folder size={16} />}
              </div>
              <span className="truncate">{folder.name}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FolderSelector;