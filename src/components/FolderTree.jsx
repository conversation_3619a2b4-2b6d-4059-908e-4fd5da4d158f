import React from 'react';
import { useFolders } from '../contexts/FolderContext.jsx';
import { ChevronRight, Folder, FolderOpen } from 'lucide-react';

const FolderTree = () => {
  const { folders, currentFolder, setCurrentFolder } = useFolders();
  const [expandedFolders, setExpandedFolders] = useState({});

  const toggleFolder = (folderId) => {
    setExpandedFolders(prev => ({
      ...prev,
      [folderId]: !prev[folderId]
    }));
  };

  const renderFolder = (folder, depth = 0) => {
    const isExpanded = expandedFolders[folder._id];
    const hasChildren = folders.some(f => f.parent === folder._id);
    
    return (
      <div key={folder._id} className="mb-1">
        <div 
          className={`flex items-center p-2 rounded cursor-pointer ${
            currentFolder?._id === folder._id 
              ? 'bg-blue-100 text-blue-800' 
              : 'hover:bg-gray-100'
          }`}
          onClick={() => setCurrentFolder(folder)}
          style={{ paddingLeft: `${depth * 24 + 8}px` }}
        >
          {hasChildren && (
            <button 
              onClick={(e) => {
                e.stopPropagation();
                toggleFolder(folder._id);
              }}
              className="mr-1"
            >
              <ChevronRight 
                size={16} 
                className={`transition-transform ${isExpanded ? 'rotate-90' : ''}`}
              />
            </button>
          )}
          <span className="mr-2">
            {isExpanded ? <FolderOpen size={16} /> : <Folder size={16} />}
          </span>
          <span className="truncate flex-1">{folder.name}</span>
          <span className="ml-2 text-xs text-gray-500">{folder.bookmarkCount}</span>
        </div>

        {isExpanded && hasChildren && (
          <div>
            {folders
              .filter(f => f.parent === folder._id)
              .map(child => renderFolder(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  // Find root folders (no parent or parent is null)
  const rootFolders = folders.filter(folder => 
    !folder.parent || folder.parent === null
  );

  return (
    <div className="folder-tree">
      <h3 className="text-lg font-semibold mb-3 flex items-center">
        <FolderOpen size={20} className="mr-2" /> Folders
      </h3>
      
      {rootFolders.length > 0 ? (
        rootFolders.map(folder => renderFolder(folder))
      ) : (
        <p className="text-gray-500 text-sm p-2">No folders created yet</p>
      )}
    </div>
  );
};

export default FolderTree;