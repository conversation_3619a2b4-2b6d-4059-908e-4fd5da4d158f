import React, { createContext, useContext, useState, useEffect } from 'react';
import { getFoldersTree } from '../utils/folderApi.js';

const FolderContext = createContext();

export const useFolders = () => useContext(FolderContext);

export const FolderProvider = ({ children }) => {
  const [folders, setFolders] = useState([]);
  const [currentFolder, setCurrentFolder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch folder tree on mount
  useEffect(() => {
    const fetchFolders = async () => {
      try {
        setLoading(true);
        const folderTree = await getFoldersTree();
        setFolders(folderTree);
        
        // Set uncategorized folder as default if exists
        const uncategorized = folderTree.find(f => f.isRoot && f.name === 'Uncategorized');
        if (uncategorized) setCurrentFolder(uncategorized);
      } catch (err) {
        setError('Failed to load folders');
        console.error('Error fetching folders:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchFolders();
  }, []);

  // Create a new folder
  const createFolder = async (folderData) => {
    try {
      // Implementation will be added when we create the API call
      console.log('Creating folder:', folderData);
      // After creating, refetch folders
      const folderTree = await getFoldersTree();
      setFolders(folderTree);
      return true;
    } catch (err) {
      console.error('Error creating folder:', err);
      return false;
    }
  };

  // Update a folder
  const updateFolder = async (folderId, updates) => {
    try {
      // Implementation will be added when we create the API call
      console.log('Updating folder:', folderId, updates);
      // After updating, refetch folders
      const folderTree = await getFoldersTree();
      setFolders(folderTree);
      return true;
    } catch (err) {
      console.error('Error updating folder:', err);
      return false;
    }
  };

  // Delete a folder
  const deleteFolder = async (folderId) => {
    try {
      // Implementation will be added when we create the API call
      console.log('Deleting folder:', folderId);
      // After deleting, refetch folders
      const folderTree = await getFoldersTree();
      setFolders(folderTree);
      
      // If current folder was deleted, reset to root
      if (currentFolder?._id === folderId) {
        const rootFolder = folderTree.find(f => f.isRoot);
        setCurrentFolder(rootFolder);
      }
      return true;
    } catch (err) {
      console.error('Error deleting folder:', err);
      return false;
    }
  };

  const value = {
    folders,
    currentFolder,
    setCurrentFolder,
    loading,
    error,
    createFolder,
    updateFolder,
    deleteFolder
  };

  return (
    <FolderContext.Provider value={value}>
      {children}
    </FolderContext.Provider>
  );
};