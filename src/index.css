@tailwind base;
@tailwind components;
@tailwind utilities;

.sticky-header-wrapper {
  position: sticky;
  top: 0;
  background-color: white; /* Adjust as needed for your app's background */
  z-index: 10; /* Ensure it stays on top of scrolling content */
  padding-bottom: 1rem; /* Add some space below the sticky header */
}

.scrollable-content-wrapper {
  max-height: calc(100vh - 200px); /* Adjust this value based on actual header height */
  overflow-y: auto; /* Enable vertical scrolling */
  padding-top: 1rem; /* To prevent content from being hidden under the sticky header */
}
